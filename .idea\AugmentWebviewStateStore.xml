<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;a8c64fc0-7175-468c-887d-b86c4bb25af8&quot;,&quot;conversations&quot;:{&quot;79728540-da85-494e-bda4-fa9e192ead4b&quot;:{&quot;id&quot;:&quot;79728540-da85-494e-bda4-fa9e192ead4b&quot;,&quot;createdAtIso&quot;:&quot;2025-07-31T02:13:26.013Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-31T02:13:26.013Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;78c15b11-6b55-4afc-adc6-90a918db6e1d&quot;},&quot;a8c64fc0-7175-468c-887d-b86c4bb25af8&quot;:{&quot;id&quot;:&quot;a8c64fc0-7175-468c-887d-b86c4bb25af8&quot;,&quot;createdAtIso&quot;:&quot;2025-07-31T02:13:26.372Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-31T02:16:24.670Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;2b5c5e8b-0ed9-4b45-b9d3-4bb650547dc6&quot;,&quot;request_message&quot;:&quot;我现在有个晶晨机顶盒安卓9，IP192.168.233.180，ADB已经打开，Frida也已经启动，我现在按首页键会等好几秒才会打开当贝桌面，先抓捕一下日志看我按首页键后都执行了什么，然后用frida进行定位，自动测试，不要用脚本，&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;我现在有个晶晨机顶盒安卓9，IP192.168.233.180，ADB已经打开，Frida也已经启动，我现在按首页键会等好几秒才会打开当贝桌面，先抓捕一下日志看我按首页键后都执行了什么，然后用frida进行定位，自动测试，不要用脚本，&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:1,&quot;type&quot;:2,&quot;content&quot;:&quot;*Your subscription <NAME_EMAIL> is inactive. If you have another account with an active subscription, please sign out and sign in with that one. Otherwise, update your plan [here](https://app.augmentcode.com/account) to continue using Augment.*&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;我现在有个晶晨机顶盒安卓9，IP192.168.233.180，ADB已经打开，Frida也已经启动，我现在按首页键会等好几秒才会打开当贝桌面，先抓捕一下日志看我按首页键后都执行了什么，然后用frida进行定位，自动测试，不要用脚本，&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;E:/ROM/MIK-4.3/311-1E软件逆向/首页键&quot;,&quot;folderRoot&quot;:&quot;E:/ROM/MIK-4.3/311-1E软件逆向/首页键&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-31T02:16:24.685Z&quot;,&quot;response_text&quot;:&quot;*Your subscription <NAME_EMAIL> is inactive. If you have another account with an active subscription, please sign out and sign in with that one. Otherwise, update your plan [here](https://app.augmentcode.com/account) to continue using Augment.*&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-dd90c103-4b11-467c-85ae-966383187ba5&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;baselineTimestamp&quot;:0,&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;c5cd6ee5-0875-45a9-bd7e-8f8c7e833f78&quot;}},&quot;agentExecutionMode&quot;:&quot;auto&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;sortConversationsBy&quot;:&quot;lastMessageTimestamp&quot;,&quot;sendMode&quot;:&quot;send&quot;}" />
      </map>
    </option>
  </component>
</project>